"use client";
import {
  Box,
  Typography,
  Grid,
  Avatar,
  Paper,
  Container,
} from "@mui/material";

const creatorsData = {
  youtubeVN: [
    { name: "ToRung", img: "/creators/torung.png" },
    { name: "BEN EAGLE", img: "/creators/beneagle.png" },
    { name: "H&T Official", img: "/creators/ht.png" },
    { name: "Like Nastya VNM", img: "/creators/nastya.png" },
    { name: "POPS Kids", img: "/creators/pops.png" },
    { name: "Trần Thị Thu Trang", img: "/creators/trang.png" },
    { name: "<PERSON>", img: "/creators/phuong.png" },
     { name: "Trần Thị Thu Trang", img: "/creators/trang.png" },
  ],
  tiktok: [
    { name: "Khabane lame", img: "/creators/khaby.png" },
    { name: "charli d'amelio", img: "/creators/charli.png" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", img: "/creators/mrbeast.png" },
    { name: "<PERSON>", img: "/creators/bella.png" },
    { name: "TikTok", img: "/creators/tiktok.png" },
    { name: "Addison", img: "/creators/addison.png" },
    { name: "Kimberly Loaiza", img: "/creators/kimberly.png" },
  ],
};

export default function HeroWithPopularCreators() {
  return (
    <Box sx={{ backgroundColor: "#af2f24", color: "white", pb: 6 }}>
      {/* HERO SECTION - CENTERED */}
      <Container sx={{ py: 10 }}>
        <Typography variant="h3" fontWeight="bold" align="center" gutterBottom>
          ANALYTICS MADE EASY
        </Typography>
        <Typography variant="h6" align="center" color="#fdd">
          Social Blade tracks daily statistics and growth for your favorite content creators that spans <br />
          across over 90 million users and 5 platforms.
        </Typography>
      </Container>

      {/* GRID FULL WIDTH - NO CONTAINER */}
      <Box>
        {/* YouTube Creators */}
        <Typography
          variant="h6"
          fontWeight="bold"
          px={2}
          mb={1}
          sx={{ color: "white" }}
        >
          Popular YouTube Creators in Vietnam
        </Typography>
        <Grid container spacing={2} sx={{ px: 2 }}>
          {creatorsData.youtubeVN.map((creator, index) => (
            <Grid item xs={6} sm={4} md={2} key={index}>
              <Paper
                elevation={2}
                sx={{
                  p: 1,
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  borderRadius: 2,
                  backgroundColor: "#eee",
                }}
              >
                <Avatar src={creator.img} alt={creator.name} />
                <Typography variant="body2" fontWeight="500">
                  {creator.name}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>

        {/* TikTok Creators */}
        <Typography
          variant="h6"
          fontWeight="bold"
          px={2}
          mt={4}
          mb={1}
          sx={{ color: "white" }}
        >
          Popular TikTok Creators
        </Typography>
        <Grid container spacing={2} sx={{ px: 2 }}>
          {creatorsData.tiktok.map((creator, index) => (
            <Grid item xs={6} sm={4} md={2} key={index}>
              <Paper
                elevation={2}
                sx={{
                  p: 1,
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  borderRadius: 2,
                  backgroundColor: "#eee",
                }}
              >
                <Avatar src={creator.img} alt={creator.name} />
                <Typography variant="body2" fontWeight="500">
                  {creator.name}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
}
