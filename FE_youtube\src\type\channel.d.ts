interface Channel {
    id: string;
    title: string;
    description: string;
    publishedAt: string;
    country: string;
    thumbnails: {
        medium: string;
        default: string;
        high: string
    };
}

interface DailyGrowth {
    date: string;
    subscribers: number;
    views: number;
}

interface Statistics {
    subscribers: number;
    videoCount: number;
    viewCount: number;
    estimatedMonthlyEarnings: {
        min: number;
        max: number;
    };
    dailyGrowth: DailyGrowth[];
}

interface ChannelType {
    channel: Channel;
    statistics: Statistics;
    futureProjections: FutureProjections[]
}
interface FutureProjections {
    views: number;
    subs: number;
    date: string;
}