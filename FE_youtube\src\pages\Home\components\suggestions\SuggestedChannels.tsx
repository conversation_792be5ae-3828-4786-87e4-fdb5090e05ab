import {
  <PERSON>,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Typography,
  Button,
} from "@mui/material";
import SubscriptionsIcon from "@mui/icons-material/Subscriptions";
import ThumbUpAltIcon from "@mui/icons-material/ThumbUpAlt";

const suggested = [
  {
    name: "<PERSON>ọ<PERSON>p Trình <PERSON>",
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_example1=s88-c-k-c0x00ffffff-no-rj",
    channelId: "UC1234567890",
  },
  {
    name: "Funny Cats Daily",
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_example2=s88-c-k-c0x00ffffff-no-rj",
    channelId: "UC9876543210",
  },
  {
    name: "<PERSON>ọ<PERSON>ập Trình <PERSON>ùng <PERSON>ha",
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_example1=s88-c-k-c0x00ffffff-no-rj",
    channelId: "UC1234567890",
  },
  {
    name: "Funny Cats Daily",
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_example2=s88-c-k-c0x00ffffff-no-rj",
    channelId: "UC9876543210",
  },
  {
    name: "Học Lập Trình Cùng Kha",
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_example1=s88-c-k-c0x00ffffff-no-rj",
    channelId: "UC1234567890",
  },
  {
    name: "Funny Cats Daily",
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_example2=s88-c-k-c0x00ffffff-no-rj",
    channelId: "UC9876543210",
  },
];

export default function SuggestedChannels() {
  return (
    <Box mt={5}>
      <Typography variant="h6" fontWeight="bold" mb={2}>
        🧾 Kênh gợi ý tương tác hôm nay
      </Typography>

      {/* Grid layout tự giãn theo không gian còn lại */}
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
          gap: 3,
        }}
      >
        {suggested.map((channel, index) => (
          <Card
            key={index}
            elevation={4}
            sx={{
              transition: "0.2s ease",
              ":hover": {
                transform: "translateY(-4px)",
              },
              borderRadius: 3,
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
            }}
          >
            <CardContent sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Avatar
                src={channel.avatar}
                alt={channel.name}
                sx={{ width: 56, height: 56 }}
              />
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  {channel.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  ID: {channel.channelId}
                </Typography>
              </Box>
            </CardContent>

            <CardActions sx={{ justifyContent: "space-between", px: 2, pb: 2 }}>
              <Button
                size="small"
                startIcon={<SubscriptionsIcon />}
                variant="outlined"
                href={`https://www.youtube.com/channel/${channel.channelId}`}
                target="_blank"
              >
                Subscribe
              </Button>
              <Button
                size="small"
                startIcon={<ThumbUpAltIcon />}
                variant="contained"
              >
                Like Video
              </Button>
            </CardActions>
          </Card>
        ))}
      </Box>
    </Box>
  );
}

