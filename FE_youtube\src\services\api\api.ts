import axios from 'axios';

const api = axios.create({

  baseURL: 'https://localhost:7003/api/',

  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// ✅ Gắn token tự động từ localStorage (nếu có login)
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// ✅ Bắt lỗi response (nếu muốn xử lý lỗi chung)
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('[API ERROR]', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export default api;
