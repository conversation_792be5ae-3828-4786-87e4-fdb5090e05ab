import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../api/api";

interface Creator {
  rank: string;
  grade: string;
  name: string;
  subscribers: string;
  views: string;
  videos: string;
  avatar?: string;
  likeCount?: string; 
}

interface LikedState {
  data: Creator[];
  loading: boolean;
  error: string | null;
}

const initialState: LikedState = {
  data: [],
  loading: false,
  error: null,
};

export const fetchTopLiked  = createAsyncThunk("user/fetchTopLiked", async (_, thunkAPI) => {
  try {
    const response = await api.get("/RankedList/top-liked");
    return response.data;
  } catch (err: any) {
    return thunkAPI.rejectWithValue(err.response?.data || "Lỗi fetch người dùng!");
  }
});


const likedSlice = createSlice({
  name: "liked",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchTopLiked.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTopLiked.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(fetchTopLiked.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Error loading data";
      });
  },
});

export default likedSlice.reducer;
