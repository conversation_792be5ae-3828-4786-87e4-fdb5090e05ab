"use client";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Typography } from "@mui/material";
import TopCreatorsTable from "./section/TopCreatorsTable";
import { fetchTopLiked } from "../../../services/redux/Slicer/user/likedSlice";
import type { AppDispatch, RootState } from "../../../services/redux/store";

// Hàm map dữ liệu từ API top-liked sang Creator[]
const mapTopLikedToCreators = (users: TopLikedUser[]) => {
  return users.map((user, index) => ({
    rank: (index + 1).toString(),
    grade: user.likeCount ? "A+" : "B", // hoặc gán cứng nếu không có logic đánh giá
    name: user.title || "Unknown",
    subscribers: user.subscriberCount?.toLocaleString() || "0",
    views: user.views?.toLocaleString() || "0",
    videos: "N/A",
    avatar: user.thumbnailUrl,
    likeCount: user.likeCount?.toLocaleString() || "0",
  }));
};
export default function TopLikedCreators() {
  const dispatch = useDispatch<AppDispatch>();
  const { data, loading, error } = useSelector((state: RootState) => state.liked);

  useEffect(() => {
    dispatch(fetchTopLiked());
  }, [dispatch]);
  const mappedCreators = mapTopLikedToCreators(data as unknown as TopLikedUser[]);

  return (
    <Box mt={6}>
      <Typography variant="h4" fontWeight="bold" mb={2}>
        ❤️ Top Liked YouTube Creators
      </Typography>

      {loading && <Typography>Đang tải dữ liệu...</Typography>}
      {error && <Typography color="error">{error}</Typography>}
      {!loading && !error && <TopCreatorsTable creators={mappedCreators} />}
    </Box>
  );
}




