import {
    Table, TableBody, TableCell, TableContainer,
    TableHead, TableRow, Paper, Typography
} from '@mui/material';

const dailyGrowth = [
    { date: "2025-05-03", subscribers: 10000, views: 115154544 },
    { date: "2025-05-04", subscribers: 12220, views: 2953321369 },
    { date: "2025-05-05", subscribers: 19000, views: 5645445454 },
    { date: "2025-05-06", subscribers: 20000, views: 295591929505 },
    { date: "2025-05-07", subscribers: 50000, views: 454564545 },
    { date: "2025-05-08", subscribers: 80000, views: 2958149307 },
    { date: "2025-05-09", subscribers: 18000, views: 29593581173 },
    { date: "2025-05-10", subscribers: 20000, views: 455454545 },
    { date: "2025-05-11", subscribers: 40000, views: 2961721931 },
    { date: "2025-05-12", subscribers: 45000, views: 1212121 },
    { date: "2025-05-13", subscribers: 56000, views: 296418801350 },
    { date: "2025-05-14", subscribers: 100000, views: 87987987845 },
    { date: "2025-05-15", subscribers: 150000, views: 29665665653 },
    { date: "2025-05-16", subscribers: 200000, views: 454545445 }
]


const formatNumber = (num: number) => {
    const absNum = Math.abs(num);
    const sign = num > 0 ? '+' : num < 0 ? '-' : '';

    if (absNum >= 1_000_000_000) return `${sign}${(absNum / 1_000_000_000).toFixed(2)}B`;
    if (absNum >= 1_000_000) return `${sign}${(absNum / 1_000_000).toFixed(1)}M`;
    if (absNum >= 1_000) return `${sign}${(absNum / 1_000).toFixed(1)}K`;
    return `${sign}${absNum}`;
};

const growthData = dailyGrowth.slice(1).map((curr, i) => {
    // console.log(dailyGrowth[i])
    const prev = dailyGrowth[i];
    return {
        date: curr.date,
        subDelta: curr.subscribers - prev.subscribers,
        viewDelta: curr.views - prev.views
    };
});

export default function DailyGrowthTable() {
    return (
        <>
            <Typography variant="h6" sx={{ mb: 2 }}>Daily Growth Table</Typography>
            <TableContainer component={Paper}>
                <Table>
                    <TableHead sx={{ backgroundColor: '#a72828' }}>
                        <TableRow>
                            <TableCell sx={{ color: 'white' }}>Date</TableCell>
                            <TableCell sx={{ color: 'white' }}>Subscribers</TableCell>
                            <TableCell sx={{ color: 'white' }}>Views</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {growthData.map((row, index) => (
                            <TableRow
                                key={index}
                                sx={{ backgroundColor: index % 2 === 0 ? '#f9f9f9' : 'white' }}
                            >
                                <TableCell>{row.date}</TableCell>
                                <TableCell sx={{ color: row.subDelta > 0 ? 'green' : 'red' }}>
                                    {formatNumber(row.subDelta)}
                                </TableCell>
                                <TableCell sx={{ color: row.viewDelta > 0 ? 'green' : 'red' }}>
                                    {formatNumber(row.viewDelta)}
                                </TableCell>
                            </TableRow>
                        ))}
                        <TableRow sx={{ borderTop: '2px solid #a72828' }}>
                            <TableCell sx={{ fontWeight: 'bold' }}>Daily Average</TableCell>
                            <TableCell sx={{ color: 'green' }}>+43K</TableCell>

                            <TableCell>$33K - $524K</TableCell>
                        </TableRow>
                        <TableRow >
                            <TableCell sx={{ fontWeight: 'bold' }}>Daily Average</TableCell>
                            <TableCell sx={{ color: 'green' }}>+43K</TableCell>

                            <TableCell>$33K - $524K</TableCell>
                        </TableRow>
                        <TableRow >
                            <TableCell sx={{ fontWeight: 'bold' }}>Daily Average</TableCell>
                            <TableCell sx={{ color: 'green' }}>+43K</TableCell>

                            <TableCell>$33K - $524K</TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </TableContainer>
        </>
    );
}
