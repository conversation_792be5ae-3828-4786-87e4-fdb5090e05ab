"use client";
import { Box, Grid, Typography } from "@mui/material";

const stats = [
  { label: "Total Creators", value: "90M" },
  { label: "YouTube", value: "69M" },
  { label: "Instagram", value: "11M" },
  { label: "TikTok", value: "1.5M" },
  { label: "Twitch", value: "7.3M" },
  { label: "Facebook", value: "1.8M" },
];

export default function PlatformStats() {
  return (
    <Box sx={{ backgroundColor: "#af2f24", py: 5 }}>
      <Grid
        container
        justifyContent="center"
        spacing={3}
        sx={{ maxWidth: "lg", margin: "0 auto", px: 2 }}
      >
        {stats.map((stat, index) => (
          <Grid
            item
            key={index}
            xs={6}
            sm={4}
            md={2}
            sx={{ textAlign: "center" }}
          >
            <Typography variant="h5" fontWeight="bold" color="white">
              {stat.value}
            </Typography>
            <Typography variant="body2" color="#fdd">
              {stat.label}
            </Typography>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
