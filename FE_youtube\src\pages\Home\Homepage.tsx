import { Container } from "@mui/material";

// import HeroSection from "./components/herosection/HeroSection";
import HeroWithPopularCreators from "./components/herosection/HeroWithPopularCreators";
import FeatureGrid from "./components/feature/FeatureGrid";
import PlatformStats from "./components/platformstat/PlatformStats";
import UserRanking from "../../components/ranking/UserRanking";
// Bạn có thể bật lại nếu cần
// import StatsOverview from "../../components/stats/StatsOverview";
// import SuggestedChannels from "../../components/suggestions/SuggestedChannels";

export default function HomePage() {
  return (
    <>
      <HeroWithPopularCreators />
       <PlatformStats />
      <FeatureGrid />

      <Container sx={{ mt: 4, mb: 8 }}>
        <UserRanking />
        {/* <StatsOverview />
        <SuggestedChannels /> */}
      </Container>
    </>
  );
}
