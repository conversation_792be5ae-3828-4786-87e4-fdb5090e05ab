import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,

  Avatar,
  Box,
} from "@mui/material";
import StarIcon from "@mui/icons-material/Star";
import WhatshotIcon from "@mui/icons-material/Whatshot";

const mockData = [
  {
    username: "KhaDevYT",
    subs: 1200,
    likes: 3400,
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_fake1=s88-c-k-c0x00ffffff-no-rj",
  },
  {
    username: "GamerZoneVN",
    subs: 980,
    likes: 4500,
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_fake2=s88-c-k-c0x00ffffff-no-rj",
  },
  {
    username: "GamerZoneVNN",
    subs: 900,
    likes: 1500,
    avatar: "https://yt3.ggpht.com/ytc/AAUvwnj_fake2=s88-c-k-c0x00ffffff-no-rj",
  },
];

export default function UserRanking() {
  return (
    <Paper
      elevation={3}
      sx={{
        mt: 4,
        borderRadius: 3,
        overflow: "hidden",
      }}
    >
      <Box
        sx={{
          backgroundColor: "#B73528",
          color: "#fff",
          px: 2,
          py: 1.5,
        }}
      >
        <Typography variant="h6" fontWeight="bold">
          📊 Top 10 Kênh Tương Tác Nhiều Nhất
        </Typography>
      </Box>

      <Table>
        <TableHead>
          <TableRow sx={{ backgroundColor: "#f5f5f5" }}>

            <TableCell>Channel</TableCell>
            <TableCell align="right">Subs</TableCell>
            <TableCell align="right">Likes</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {mockData.map((row, index) => (
            <TableRow
              key={index}
              hover
              sx={{
                backgroundColor: index === 0 ? "#fff8e1" : "inherit",
              }}
            >
              <TableCell>
                <Box display="flex" alignItems="center" gap={2}>
                  <Avatar src={row.avatar} alt={row.username} />
                  <Box>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {row.username}
                    </Typography>
                    {index === 0 && (
                      <Typography
                        variant="caption"
                        color="warning.main"
                        display="flex"
                        alignItems="center"
                        gap={0.5}
                      >
                        <StarIcon fontSize="small" />
                        Đứng đầu bảng
                      </Typography>
                    )}
                  </Box>
                </Box>
              </TableCell>
              <TableCell align="right">{row.subs.toLocaleString()}</TableCell>
              <TableCell align="right">
                {row.likes.toLocaleString()}
                {index === 0 && (
                  <WhatshotIcon
                    fontSize="small"
                    color="error"
                    sx={{ ml: 1 }}
                    titleAccess="Hot channel"
                  />
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Paper>
  );
}
