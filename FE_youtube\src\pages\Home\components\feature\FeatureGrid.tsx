import {
  Box,
  Typography,
  Link,
  SvgIcon,
  Container,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import BarChartIcon from "@mui/icons-material/BarChart";
import TerminalIcon from "@mui/icons-material/Terminal";

const features = [
  {
    icon: PersonOutlineIcon,
    title: "Personalize Your Experience",
    desc:
      "Create a free account for instant access to your favorite creators' analytics. Upgrade to a paid account to unlock more features.",
    links: [{ label: "Create a Social Blade Account", href: "#" }],
  },
  {
    icon: TrendingUpIcon,
    title: "See Top Creators",
    desc: "Explore Top Lists to see who is popular on each platform.",
    links: [
      { label: "Top YouTubers", href: "#" },
      { label: "Top Instagram Creators", href: "#" },
      { label: "Top TikTokers", href: "#" },
      { label: "Top Twitch Streamers", href: "#" },
    ],
  },
  {
    icon: BarChartIcon,
    title: "Explore Your Niche",
    desc:
      "If you are a business or entity looking for data then we have solutions for you with our query builder.",
    links: [{ label: "Learn about Social Blade Reports", href: "#" }],
  },
  {
    icon: TerminalIcon,
    title: "Business API",
    desc:
      "Our Paid API allows you to integrate the power of Social Blade directly into your system.",
    links: [{ label: "Learn about our Business API", href: "#" }],
  },
];

export default function FeatureGrid() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Box py={8} sx={{ backgroundColor: "#fafafa" }}>
      <Container maxWidth="xl">
        <Box
          display="flex"
          flexDirection={isMobile ? "column" : "row"}
          flexWrap="wrap"
          gap={4}
        >
          {features.map((feature, i) => (
            <Box
              key={i}
              flex={1}
              minWidth={isMobile ? "100%" : "230px"}
              sx={{ maxWidth: "100%" }}
            >
              <Box mb={1}>
                <SvgIcon
                  component={feature.icon}
                  sx={{ fontSize: 36, color: "#222" }}
                />
              </Box>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                {feature.title}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                mb={1}
                sx={{ lineHeight: 1.6 }}
              >
                {feature.desc}
              </Typography>
              {feature.links.map((link, j) => (
                <Link
                  key={j}
                  href={link.href}
                  underline="hover"
                  sx={{
                    display: "inline-block",
                    mr: 1,
                    mb: 0.5,
                    color: "#b73528",
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  {link.label}
                </Link>
              ))}
            </Box>
          ))}
        </Box>
      </Container>
    </Box>
  );
}
