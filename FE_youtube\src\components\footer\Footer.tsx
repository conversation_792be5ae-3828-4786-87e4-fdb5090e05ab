import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON>Button,
  Button,
} from "@mui/material";
import YouTubeIcon from "@mui/icons-material/YouTube";
import InstagramIcon from "@mui/icons-material/Instagram";
import FacebookIcon from "@mui/icons-material/Facebook";
import TwitterIcon from "@mui/icons-material/X";
import SportsEsportsIcon from "@mui/icons-material/SportsEsports"; // Twitch icon

export default function Footer() {
  return (
    <Box
      sx={{
        backgroundColor: "#f5f5f5",
        pt: 6,
        pb: 3,
        mt: 8,
        borderTop: "1px solid #ddd",
      }}
    >
      <Container
        maxWidth="xl"
        sx={{
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Grid container spacing={4} maxWidth="lg">
          {/* Logo + Social */}
          <Grid
            item
            xs={12}
            md={4}
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: { xs: "center", md: "flex-start" },
              textAlign: { xs: "center", md: "left" },
            }}
          >
            <Typography variant="h6" fontWeight="bold" color="text.primary" mb={1}>
              <Box component="span" sx={{ color: "#b73528" }}>▮</Box> SOCIAL{" "}
              <Box component="span" fontWeight="bold">BLADE</Box>
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={2}>
              Copyright ©2008–2025 Social Blade, LLC
            </Typography>

            <Box display="flex" gap={1} mb={2}>
              <IconButton><YouTubeIcon /></IconButton>
              <IconButton><TwitterIcon /></IconButton>
              <IconButton><InstagramIcon /></IconButton>
              <IconButton><FacebookIcon /></IconButton>
              <IconButton><SportsEsportsIcon /></IconButton>
            </Box>

            <Button
              variant="contained"
              sx={{
                backgroundColor: "#b73528",
                ":hover": { backgroundColor: "#9d2f24" },
                borderRadius: 1,
              }}
            >
              CONTACT SUPPORT
            </Button>
          </Grid>

          {/* About */}
          <Grid item xs={6} md={2}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              About
            </Typography>
            {[
              "About Us",
              "Terms of Service",
              "Privacy Policy",
              "Meet the Team",
              "Press Inquiries",
              "Contact Us",
            ].map((item, i) => (
              <Link
                key={i}
                href="#"
                underline="hover"
                color="text.secondary"
                display="block"
                sx={{ mb: 0.5 }}
              >
                {item}
              </Link>
            ))}
          </Grid>

          {/* More Social Blade */}
          <Grid item xs={6} md={3}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              More Social Blade
            </Typography>
            {[
              "Social Blade Blog",
              "Social Blade Business API",
              "YouTube EDU",
              "YouTube Consulting",
              "YouTube Money Calculator / Estimated Earnings",
            ].map((item, i) => (
              <Link
                key={i}
                href="#"
                underline="hover"
                color="text.secondary"
                display="block"
                sx={{ mb: 0.5 }}
              >
                {item}
              </Link>
            ))}
          </Grid>

          {/* Helpful Pages */}
          <Grid item xs={12} md={3}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Helpful Pages
            </Typography>
            {[
              "Connect Your Social Media",
              "Find Influencers – Run Reports",
              "Remove Ads & Get More Features",
              "Join our Discord",
            ].map((item, i) => (
              <Link
                key={i}
                href="#"
                underline="hover"
                color="text.secondary"
                display="block"
                sx={{ mb: 0.5 }}
              >
                {item}
              </Link>
            ))}
            <Typography
              variant="caption"
              color="text.disabled"
              sx={{ mt: 2, display: "inline-block" }}
            >
              fda3d106
            </Typography>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}
