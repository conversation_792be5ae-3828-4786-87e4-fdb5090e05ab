import { configureStore } from '@reduxjs/toolkit';
import userReducer from './Slicer/user/userSlice';
import channelReducer from '../redux/Slicer/channelSlice';
import toplikeuser from './Slicer/user/likedSlice';



export const store = configureStore({
  reducer: {
    user: userReducer,
    liked: toplikeuser, // Thêm reducer cho top liked
    channel: channelReducer

  },
  // Optional: thêm middleware nếu bạn dùng logger hay API middleware
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false, // nếu có lỗi liên quan đến non-serializable như FormData
    }),
});

// 🔥 Types cho dispatch & state (để dùng trong TS project)
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
