import React from "react";
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  Avatar,
  Box,
  Typography,
  Button,
} from "@mui/material";
import { useNavigate } from "react-router";

interface Creator {
  rank: string;
  grade: string;
  name: string;
  subscribers: string;
  views: string;
  videos: string;
  avatar?: string;
  likeCount?: string; // Thêm trường này nếu cần
}

interface TopCreatorsTableProps {
  creators: Creator[];
}

const TopCreatorsTable: React.FC<TopCreatorsTableProps> = ({ creators }) => {
  const navigate = useNavigate();

  const handleGoToTopLists = () => {
    navigate("/filter");
  };

  return (
    <TableContainer
      component={Paper}
      sx={{
        width: "100%",
        overflowX: "auto",
        borderRadius: 2,
        flex: 1, // để nó chia đều khi nằm cạnh nhau
        minWidth: 0, // tránh ép rộng hơn container
      }}
    >
      <Table>
        <TableHead>
          <TableRow>
            <TableCell colSpan={6} align="left">
              <Typography variant="h6" fontWeight="bold">
                Top YouTube Creators by Social Blade Rank
              </Typography>
            </TableCell>
          </TableRow>
          <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
            <TableCell>#</TableCell>
            <TableCell>Grade</TableCell>
            <TableCell>Name</TableCell>
            <TableCell>Subscribers</TableCell>
            <TableCell>Like</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {creators.map((creator, index) => (
            <TableRow key={index}>
              <TableCell>{creator.rank}</TableCell>
              <TableCell>
                <Typography fontWeight="bold" color="primary">
                  {creator.grade}
                </Typography>
              </TableCell>
              <TableCell>
                <Box display="flex" alignItems="center" gap={1}>
                  {creator.avatar && (
                    <Avatar src={creator.avatar} alt={creator.name} />
                  )}
                  <Typography>{creator.name}</Typography>
                </Box>
              </TableCell>
              <TableCell>{creator.subscribers}</TableCell>
              <TableCell>{creator.likeCount}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Box p={2} textAlign="right">
        <Button
          variant="contained"
          color="error"
          size="small"
          onClick={handleGoToTopLists}
        >
          GO TO TOP LISTS
        </Button>
      </Box>
    </TableContainer>
  );
};

export default TopCreatorsTable;
