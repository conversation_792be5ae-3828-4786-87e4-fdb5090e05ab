{"name": "fe-youtube", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@reduxjs/toolkit": "^2.8.2", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "dotenv": "^16.5.0", "fe-youtube": "file:", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router": "^7.6.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-redux": "^7.1.34", "@types/recharts": "^2.0.1", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}