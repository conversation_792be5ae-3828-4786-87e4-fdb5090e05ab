
import { useEffect, useState } from "react";
import {
    Card,
    CardContent,
    CardHeader,
    CardMedia,
    Typography,
    Grid,
    CircularProgress,
    Box,
} from "@mui/material";
import SynchronizedLineChart from "../../components/chart/SynchronizedLineChart"
import ChannelMetricsTable from "../../components/table/ChannelMetricsTable";
import api from "../../services/api/api";
// import { RootState, AppDispatch } from '../services/redux/store';
// import { useDispatch, useSelector } from 'react-redux';


const mockData: ChannelType= {
    channel: {
        id: "UCq-Fj5jknLsUf-MWSy4_brA",
        title: "T-Series",
        description: "Music can change the world. T-Series is India's largest Music Label & Movie Studio...",
        publishedAt: "2006-03-13T07:44:00Z",
        country: "IN",
        thumbnails: {
            default: "https://images.socialblade.com/256x,q75/https://yt3.ggpht.com/VunTf0NzCeboiPjbesBdnQuxaF3Lja7UGRbBGQAWRJgMSTj9TTLO3pS1X9qPOJGCNnmPrXeY=s192-c-k-c0x00ffffff-no-rj",
            medium: "https://images.socialblade.com/256x,q75/https://yt3.ggpht.com/VunTf0NzCeboiPjbesBdnQuxaF3Lja7UGRbBGQAWRJgMSTj9TTLO3pS1X9qPOJGCNnmPrXeY=s192-c-k-c0x00ffffff-no-rj",
            high: "https://images.socialblade.com/1920x,q75/https://yt3.googleusercontent.com/AdqTv_VGdYnL4B73UGBVikQ40wJx8wp6DzesqPmESBtYzEvT-TGvOVCqnWDVmEQqDnQ8Shdj=w2560-fcrop64=1,00005a57ffffa5a8-k-c0xffffffff-no-nd-rj"
        }
    },
    statistics: {
        subscribers: 294000000,
        videoCount: 20778,
        viewCount: ************,
        estimatedMonthlyEarnings: {
            min: 375000,
            max: 6000000
        },
        dailyGrowth: [
            { date: "2025-05-03", subscribers: 10000, views: 115154544 },
            { date: "2025-05-04", subscribers: 12220, views: 2953321369 },
            { date: "2025-05-05", subscribers: 19000, views: 5645445454 },
            { date: "2025-05-06", subscribers: 20000, views: 295591929505 },
            { date: "2025-05-07", subscribers: 50000, views: 454564545 },
            { date: "2025-05-08", subscribers: 80000, views: 2958149307 },
            { date: "2025-05-09", subscribers: 18000, views: 29593581173 },
            { date: "2025-05-10", subscribers: 20000, views: 455454545 },
            { date: "2025-05-11", subscribers: 40000, views: 2961721931 },
            { date: "2025-05-12", subscribers: 45000, views: 1212121 },
            { date: "2025-05-13", subscribers: 56000, views: 296418801350 },
            { date: "2025-05-14", subscribers: 100000, views: 87987987845 },
            { date: "2025-05-15", subscribers: 150000, views: 29665665653 },
            { date: "2025-05-16", subscribers: 200000, views: 454545445 }
        ]
    },
    ranking: {
        socialbladeRank: 1,
        subscribersRank: 1,
        viewsRank: 1,
        countryRank: 1,
        categoryRank: 1
    },
    futureProjections: [
        { date: "2025-06-01", subs: 295500000, views: 298500000000 },
        { date: "2025-07-01", subs: 88454545, views: 4484484545 },
        { date: "2025-08-01", subs: 298500000, views: 301500000000 },
        { date: "2025-09-01", subs: 300000000, views: 200015 },
        { date: "2025-10-01", subs: 4545151, views: 304500000000 },
        { date: "2025-11-01", subs: 303000000, views: 306000000000 }
    ],
    latestVideos: [
        {
            id: "XxXxXxX1",
            title: "Latest Hindi Song 2025 | T-Series",
            publishedAt: "2025-05-15T09:00:00Z",
            thumbnail: "https://i.ytimg.com/vi/XxXxXxX1/hqdefault.jpg",
            views: 3200000
        },
        {
            id: "XxXxXxX2",
            title: "Romantic Song 2025 | T-Series",
            publishedAt: "2025-05-14T09:00:00Z",
            thumbnail: "https://i.ytimg.com/vi/XxXxXxX2/hqdefault.jpg",
            views: 2800000
        }
    ]
};
const getChannel = async () => {
    const res = await api.get('Youtube/channel-by-id?channelId=UCGO3xOdjuKhULFkwwPoE3Mg')
    // const data = await res.json()
    console.log(res.data)
}
function ChannelPage() {

    const [data, setData] = useState<ChannelType | null>(null);
    // const dispatch = useDispatch<AppDispatch>();
    // const { data } = useSelector((state: RootState) => state.channel);

    // useEffect(() => {
    //     dispatch(fetchChannelById(id));
    // }, [id])
    useEffect(() => {
        getChannel()
        setTimeout(() => setData(mockData), 300);
    }, []);

    if (!data)
        return (
            <Box display="flex" justifyContent="center" mt={10}>
                <CircularProgress />
            </Box>
        );

    return (
        <Box maxWidth={"80%"} mx="auto">
            <Box
                sx={{
                    height: { xs: 150, sm: 250 },
                    backgroundImage: `url(${data.channel.thumbnails.high || "/banner-placeholder.jpg"})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                }}
            />
            <Card sx={{ display: "flex", flexDirection: { xs: "column", sm: "row" }, backgroundColor: "#a43024", borderRadius: 0, overflow: "initial", p: "0px 24px" }}>

                <CardHeader
                    avatar={
                        <CardMedia
                            component="img"
                            image={data.channel.thumbnails.default}
                            alt="logo"
                            sx={{ width: 112, height: 112, marginTop: "-64px" }}
                        />
                    }
                />
                <CardContent>
                    <Grid container spacing={3} width={"100%"} gritGapColumns={"80px"}>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="white">
                                Subscribers
                            </Typography>
                            <Typography variant="h5" color="white" mt={1}>
                                {data.statistics.subscribers.toLocaleString()}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="white">
                                Views
                            </Typography>
                            <Typography variant="h5" color="white" mt={1}>
                                {data.statistics.viewCount.toLocaleString()}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <Typography variant="body2" color="white">
                                Videos
                            </Typography>
                            <Typography variant="h5" color="white" mt={1}>
                                {data.statistics.videoCount.toLocaleString()}
                            </Typography>
                        </Grid>
                    </Grid>

                </CardContent>
            </Card>
            <Box>
                <ChannelMetricsTable></ChannelMetricsTable>
            </Box>
            <Box mt={3} sx={{ boxShadow: "0 0 5px gray", p: 3, borderRadius: 2 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: "bold", fontSize: "24px" }} gutterBottom>
                    Monthly Gained Subscribers for []
                </Typography>
                <SynchronizedLineChart data={data.statistics.dailyGrowth} type="views">

                </SynchronizedLineChart>
            </Box>
            <Box mt={3} sx={{ boxShadow: "0 0 5px gray", p: 3, borderRadius: 2 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: "bold", fontSize: "24px" }} gutterBottom>
                    Monthly Gained Views for []
                </Typography>
                <SynchronizedLineChart data={data.statistics.dailyGrowth} type="subscribers">

                </SynchronizedLineChart>
            </Box>
        </Box >
    );
}

export default ChannelPage;

