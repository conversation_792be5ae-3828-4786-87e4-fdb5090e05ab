import HomePage from '../pages/Home/Homepage.tsx';
import MainLayout from '../layout/MainLayout.tsx';
import {
    createBrowserRouter,
} from "react-router";
import ChannelPage from '../pages/Detail/ChannelPage.tsx';
import Youtube from '../pages/Youtube/Youtube.tsx';
import  FilterPage  from '../pages/Filter/FilterPage.tsx';
import CustomReports from '../pages/CustomReports/CustomReports.tsx';
import PointSystemPage from '../pages/PointSystem/PointSystemPage.tsx';

const router = createBrowserRouter([
    {
        path: "/",
        element: <MainLayout></MainLayout>,
        children: [
            {
                index: true,
                element: <HomePage />,
            },
            {

                path: "channel/:name",
                element: <ChannelPage></ChannelPage>
            },
            {
                path: "youtube",
                element: <Youtube />,
            },
            {
                path: "filter",
                element: <FilterPage />,
            },
            {
                path: "custom-reports",
                element: <CustomReports />,
            },
            {
                path: "point-system",
                element: <PointSystemPage />,
            }
        ],
    },
    {
        path: '*',
        element: <div>Not Found</div>,
    },
]);
export default router
