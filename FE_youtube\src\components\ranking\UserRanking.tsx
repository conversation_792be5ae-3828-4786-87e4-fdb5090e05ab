"use client";

import { useState } from "react";
import { Box, Stack, Typography, Paper } from "@mui/material";

const tabs = [
  {
    label: "Search",
    description:
      "At the top of the site you will find a search box. Chose the platform you are interested in, type in the profile name, and then view the results!",
    image: "/images/search.png",
  },
  {
    label: "Creator Stats",
    description:
      "Check detailed statistics about creators across platforms.",
    image: "/images/creator-stats.png",
  },
  {
    label: "Video Stats",
    description: "Get insights on video performance and analytics.",
    image: "/images/video-stats.png",
  },
  {
    label: "Favorites",
    description: "Save your favorite creators for quick access.",
    image: "/images/favorites.png",
  },
  {
    label: "Top Lists",
    description:
      "Browse top-performing creators by country and category.",
    image: "/images/top-lists.png",
  },
];

export default function FeatureTabs() {
  const [activeTab, setActiveTab] = useState<number | null>(null);

  return (
    <Box display="flex" flexDirection={{ xs: "column", md: "row" }} gap={4} mt={4}>
      {/* Left column */}
      <Box flex={1}>
        <Stack spacing={2}>
          {tabs.map((tab, index) => {
            const isActive = activeTab === index;

            return (
              <Paper
                key={tab.label}
                elevation={isActive ? 6 : 2}
                onClick={() => setActiveTab(index)}
                sx={{
                  cursor: "pointer",
                  backgroundColor: "#ddd",
                  padding: 2,
                  borderRadius: 2,
                  position: "relative",
                  border: isActive ? "2px solid #b15b5b" : "1px solid #ccc",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    backgroundColor: "#ccc",
                  },
                }}
              >
                <Typography variant="h6" fontWeight="bold" mb={1}>
                  {tab.label}
                </Typography>

                {isActive && (
                  <Typography fontSize={14} sx={{ transition: "opacity 0.3s" }}>
                    {tab.description}
                  </Typography>
                )}

                {isActive && (
                  <Box
                    sx={{
                      height: "6px",
                      backgroundColor: "#b15b5b",
                      borderRadius: 2,
                      position: "absolute",
                      bottom: 0,
                      left: 0,
                      width: "25%",
                    }}
                  />
                )}
              </Paper>
            );
          })}
        </Stack>
      </Box>

      {/* Right column */}
      <Box
        flex={2}
        display="flex"
        justifyContent="center"
        alignItems="center"
        sx={{
          backgroundColor: "#af2f24",
          borderRadius: 2,
          minHeight: 360,
        }}
      >
        {activeTab !== null ? (
          <img
            src={tabs[activeTab].image}
            alt={tabs[activeTab].label}
            style={{
              maxWidth: "100%",
              maxHeight: "360px",
              objectFit: "contain",
              borderRadius: "8px",
            }}
          />
        ) : (
          <Typography variant="h6" color="white">
            Select a feature to view
          </Typography>
        )}
      </Box>
    </Box>
  );
}
