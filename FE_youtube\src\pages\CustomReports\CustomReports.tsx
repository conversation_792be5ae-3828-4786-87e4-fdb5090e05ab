import React, { useState } from "react";
import {
  Box,
  Container,
  Paper,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import {
  Person as PersonIcon,
  Receipt as BillingIcon,
  Link as ConnectionsIcon,
  Assessment as ReportsIcon,
  CreditCard as ReportCardsIcon,
} from "@mui/icons-material";
import AccountComponent from "./AccountComponent";

// Navigation items with icons
const navigationItems = [
  {
    id: "account",
    label: "Account",
    icon: PersonIcon,
  },
  {
    id: "billing",
    label: "Billing",
    icon: BillingIcon,
  },
  {
    id: "connections",
    label: "Connections",
    icon: ConnectionsIcon,
  },
  {
    id: "reports",
    label: "Reports",
    icon: ReportsIcon,
  },
  {
    id: "report-cards",
    label: "Report Cards",
    icon: ReportCardsIcon,
  },
];

const CustomReports: React.FC = () => {
  const [selectedItem, setSelectedItem] = useState<string>("account");

  const handleItemClick = (itemId: string) => {
    setSelectedItem(itemId);
  };

  const renderContent = () => {
    switch (selectedItem) {
      case "account":
        return (
          <Box>
            <AccountComponent />
          </Box>
        );
      case "billing":
        return (
          <Box>
            <Typography variant="h4" fontWeight="bold" mb={3}>
              Billing Information
            </Typography>
            <Typography variant="body1" color="text.secondary">
              View and manage your billing information and payment methods.
            </Typography>
          </Box>
        );
      case "connections":
        return (
          <Box>
            <Typography variant="h4" fontWeight="bold" mb={3}>
              Connections
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage your social media connections and integrations.
            </Typography>
          </Box>
        );
      case "reports":
        return (
          <Box>
            <Typography variant="h4" fontWeight="bold" mb={3}>
              Reports
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Access and generate custom reports for your channels.
            </Typography>
          </Box>
        );
      case "report-cards":
        return (
          <Box>
            <Typography variant="h4" fontWeight="bold" mb={3}>
              Report Cards
            </Typography>
            <Typography variant="body1" color="text.secondary">
              View detailed report cards and analytics.
            </Typography>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 8 }}>
      <Box sx={{ display: "flex", gap: 3, alignItems: "flex-start" }}>
        {/* Sidebar Navigation */}
        <Paper
          elevation={2}
          sx={{
            width: 250,
            height: "fit-content",
            borderRadius: 2,
            position: "sticky",
            top: 20,
          }}
        >
          <List sx={{ p: 0 }}>
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              const isSelected = selectedItem === item.id;

              return (
                <React.Fragment key={item.id}>
                  <ListItem disablePadding>
                    <ListItemButton
                      onClick={() => handleItemClick(item.id)}
                      sx={{
                        py: 1.5,
                        px: 2,
                        backgroundColor: isSelected ? "#B73528" : "transparent",
                        color: isSelected ? "white" : "text.primary",
                        "&:hover": {
                          backgroundColor: isSelected ? "#B73528" : "#f5f5f5",
                        },
                        borderRadius: 0,
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          color: isSelected ? "white" : "text.secondary",
                          minWidth: 40,
                        }}
                      >
                        <IconComponent />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography
                            sx={{
                              fontWeight: isSelected ? "bold" : "medium",
                              fontSize: "16px",
                            }}
                          >
                            {item.label}
                          </Typography>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                </React.Fragment>
              );
            })}
          </List>
        </Paper>

        {/* Main Content Area */}
        <Paper
          elevation={2}
          sx={{
            flex: 1,
            p: 4,
            borderRadius: 2,
            minHeight: 500,
          }}
        >
          {renderContent()}
        </Paper>
      </Box>
    </Container>
  );
};

export default CustomReports;
