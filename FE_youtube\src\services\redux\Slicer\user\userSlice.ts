// src/redux/features/user/userSlice.ts
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../api/api"; // <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON> setup alias

interface User {
  channelId: string;
  title: string;
  thumbnailUrl: string;
  subscriberCount: number;
  likeCount: number | null;
}

interface UserState {
  users: User[];
  loading: boolean;
  error: string | null;
}

const initialState: UserState = {
  users: [],
  loading: false,
  error: null,
};

export const fetchUsers = createAsyncThunk("user/fetchUsers", async (_, thunkAPI) => {
  try {
    const response = await api.get("/RankedList/top-subscribed");
    return response.data;
  } catch (err: any) {
    return thunkAPI.rejectWithValue(err.response?.data || "Lỗi fetch người dùng!");
  }
});

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.users = action.payload;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export default userSlice.reducer;
