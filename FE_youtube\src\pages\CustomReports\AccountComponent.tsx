import React, { useState } from "react";
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
} from "@mui/material";

const AccountComponent: React.FC = () => {
  const [displayName, setDisplayName] = useState<string>("");
  const [defaultLocation, setDefaultLocation] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [newPassword, setNewPassword] = useState<string>("");

  const handleSaveBasics = () => {
    console.log("Saving Account Basics:", { displayName, defaultLocation });
  };

  const handleSaveSecurity = () => {
    console.log("Saving Security Settings:", { email, newPassword });
  };

  const handleLocateMe = () => {
    console.log("Locating user...");
  };

  const handleResendVerification = () => {
    console.log("Resending verification email...");
  };

  const handleGenerateQR = () => {
    console.log("Generating QR Code for 2FA...");
  };

  return (
    <Box>
      {/* Account Basics Section */}
      <Paper
        elevation={0}
        sx={{
          mb: 3,
          borderTop: "3px solid #ff6b35",
          borderLeft: "1px solid #ff6b35",
          borderRight: "1px solid #ff6b35",
          borderBottom: "1px solid #ff6b35",
          borderRadius: 0,
          backgroundColor: "#f8f9fa"
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: "1px solid #e0e0e0"
          }}
        >
          <Typography variant="h6" fontWeight="bold">
            Account Basics
          </Typography>
        </Box>

        {/* Form Content */}
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            {/* Display Name */}
            <Box>
              <Typography variant="body2" fontWeight="medium" mb={1}>
                Display Name
              </Typography>
              <TextField
                fullWidth
                placeholder="Display Name"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                size="small"
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "#d0d0d0",
                    },
                  }
                }}
              />
            </Box>

            {/* Default Location */}
            <Box>
              <Typography variant="body2" fontWeight="medium" mb={1}>
                Default Location
              </Typography>
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <TextField
                  fullWidth
                  placeholder="Default Location"
                  value={defaultLocation}
                  onChange={(e) => setDefaultLocation(e.target.value)}
                  size="small"
                  sx={{
                    backgroundColor: "white",
                    "& .MuiOutlinedInput-root": {
                      "& fieldset": {
                        borderColor: "#d0d0d0",
                      },
                    }
                  }}
                />
                <Button
                  variant="contained"
                  size="small"
                  onClick={handleLocateMe}
                  sx={{
                    backgroundColor: "#666",
                    "&:hover": { backgroundColor: "#555" },
                    minWidth: "100px"
                  }}
                >
                  Locate me
                </Button>
              </Box>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: "block" }}>
                This default location will be used when you authenticate/verify any new accounts on any platform. Start by typing in your city.
              </Typography>
            </Box>
          </Box>

          {/* Save Button */}
          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 3 }}>
            <Button
              variant="contained"
              onClick={handleSaveBasics}
              sx={{
                backgroundColor: "#666",
                "&:hover": { backgroundColor: "#555" }
              }}
            >
              Save
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Security Section */}
      <Paper
        elevation={0}
        sx={{
          mb: 3,
          border: "1px solid #ff6b35",
          borderRadius: 0,
          backgroundColor: "#f8f9fa"
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: "1px solid #e0e0e0"
          }}
        >
          <Typography variant="h6" fontWeight="bold">
            Security
          </Typography>
        </Box>

        {/* Form Content */}
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            {/* Email */}
            <Box>
              <Typography variant="body2" fontWeight="medium" mb={1}>
                Email
              </Typography>
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <TextField
                  fullWidth
                  placeholder="Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  size="small"
                  sx={{
                    backgroundColor: "white",
                    "& .MuiOutlinedInput-root": {
                      "& fieldset": {
                        borderColor: "#d0d0d0",
                      },
                    }
                  }}
                />
                <Button
                  variant="contained"
                  color="error"
                  size="small"
                  onClick={handleResendVerification}
                >
                  Resend Verification
                </Button>
              </Box>
            </Box>

            {/* New Password */}
            <Box>
              <Typography variant="body2" fontWeight="medium" mb={1}>
                New Password
              </Typography>
              <TextField
                fullWidth
                type="password"
                placeholder="New Password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                size="small"
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "#d0d0d0",
                    },
                  }
                }}
              />
            </Box>

            {/* Enable 2FA */}
            <Box>
              <Typography variant="body2" fontWeight="medium" mb={1}>
                Enable 2FA
              </Typography>
              <Button
                variant="contained"
                fullWidth
                onClick={handleGenerateQR}
                sx={{
                  backgroundColor: "#666",
                  "&:hover": { backgroundColor: "#555" },
                  py: 1.5
                }}
              >
                Generate QR Code
              </Button>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: "block" }}>
                Enabling Two-Factor Authentication is a way to provide an extra security step on your account making sure that only you can login.
              </Typography>
            </Box>
          </Box>

          {/* Save Button */}
          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mt: 3 }}>
            <Typography variant="body2" color="text.secondary">
              Don't forget to save
            </Typography>
            <Button
              variant="contained"
              onClick={handleSaveSecurity}
              sx={{
                backgroundColor: "#666",
                "&:hover": { backgroundColor: "#555" }
              }}
            >
              Save
            </Button>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default AccountComponent;
