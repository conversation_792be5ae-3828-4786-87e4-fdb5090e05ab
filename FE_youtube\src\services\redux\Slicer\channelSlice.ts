import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../api/api';

export const fetchChannelById = createAsyncThunk(
    'channel/fetchChannelById',
    async (id: string) => {
        const response = await api.get(`Youtube/channel-by-id?channelId=${id}`);
        return response.data;
    }
);

interface ChannelState {
    data: ChannelType | null;
    loading: boolean;
    error: string | null;
}

const initialState: ChannelState = {
    data: null,
    loading: false,
    error: null,
};

const channelSlice = createSlice({
    name: 'channel',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchChannelById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchChannelById.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(fetchChannelById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message || 'Lỗi không xác đ<PERSON>nh';
            });
    },
});

export default channelSlice.reducer;
