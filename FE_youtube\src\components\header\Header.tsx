import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Box,
  Button,
  IconButton,
  InputBase,
  Menu,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemText,
  useMediaQuery,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import MenuIcon from "@mui/icons-material/Menu";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useState } from "react";
import { useTheme } from "@mui/material/styles";
import { useNavigate } from "react-router";

interface TabItem {
  label: string;
  href: string;
}

export default function Header() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const openMenu = Boolean(anchorEl);
  const toggleDrawer = () => setDrawerOpen((prev) => !prev);

  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => setAnchorEl(null);

  const handleCustomReportsClick = () => {
    navigate("/custom-reports");
    handleMenuClose();
  };

  const handleDrawerItemClick = (item: TabItem | string) => {
    if (typeof item === 'string') {
      // Handle string items (like "Products and Services", "FAQ", etc.)
      if (item === "Products and Services") {
        // Could open a menu or navigate to a specific page
        return;
      } else if (item === "Point System") {
        navigate("/point-system");
      }
    } else {
      // Handle TabItem objects
      navigate(item.href);
    }
    setDrawerOpen(false);
  };

  const tabs: TabItem[] = [
    { label: "YouTube", href: "/youtube" },
    { label: "TikTok", href: "/tiktok" },
    { label: "Twitch", href: "/twitch" },
    { label: "Facebook", href: "/facebook" },
    { label: "Instagram", href: "/instagram" },
  ];
  const extras = ["FAQ", "Contact Support"];
  const drawerItems: (TabItem | string)[] = [...tabs, "Products and Services", "Point System", ...extras];

  return (
    <>
      {/* Top Header */}
      <AppBar
        position="static"
        sx={{ backgroundColor: "#B73528", boxShadow: "none" }}
      >
        <Toolbar sx={{ justifyContent: "space-between" }}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" sx={{ fontWeight: "bold" }}>
              SOCIAL
            </Typography>
            <Typography variant="h6">BLADE</Typography>
          </Box>

          {!isMobile && (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                backgroundColor: "#a43024",
                padding: "4px 8px",
                borderRadius: 1,
                width: "40%",
              }}
            >
              <SearchIcon sx={{ mr: 1 }} />
              <InputBase
                placeholder="Search for your favorite YouTube creator"
                sx={{ color: "white", width: "100%" }}
              />
            </Box>
          )}

          <Box>
            <Button color="inherit">Sign in</Button>
            <Typography component="span" sx={{ mx: 1 }}>
              or
            </Typography>
            <Button color="inherit">Sign up</Button>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Bottom Tabs */}
      <AppBar
        position="static"
        sx={{ backgroundColor: "#9D2F22", boxShadow: "none" }}
      >
        <Toolbar sx={{ justifyContent: "space-between" }}>
          {isMobile ? (
            <>
              <IconButton color="inherit" onClick={toggleDrawer}>
                <MenuIcon />
              </IconButton>
              <Drawer anchor="left" open={drawerOpen} onClose={toggleDrawer}>
                <Box
                  sx={{ width: 250 }}
                  role="presentation"
                >
                  <List>
                    {drawerItems.map((item, index) => (
                      <ListItem
                        component="button"
                        key={index}
                        onClick={() => handleDrawerItemClick(item)}
                      >
                        <ListItemText primary={typeof item === 'string' ? item : (item as TabItem).label} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Drawer>
            </>
          ) : (
            <>
              <Box display="flex" gap={2}>
                {tabs.map(({ label, href }) => (
                  <Button key={label} color="inherit" component="a" href={href}>
                    {label}
                  </Button>
                ))}
              </Box>

              <Box display="flex" gap={2} alignItems="center">
                <Button
                  color="inherit"
                  endIcon={<ExpandMoreIcon />}
                  onClick={handleMenuOpen}
                >
                  Products and Services
                </Button>
                <Menu
                  anchorEl={anchorEl}
                  open={openMenu}
                  onClose={handleMenuClose}
                >
                  <MenuItem onClick={handleMenuClose}>
                    YouTube Consulting
                  </MenuItem>
                  <MenuItem onClick={handleMenuClose}>Business API</MenuItem>
                  <MenuItem onClick={handleCustomReportsClick}>Custom Reports</MenuItem>
                  <MenuItem onClick={() => { navigate("/point-system"); handleMenuClose(); }}>
                    Point System
                  </MenuItem>
                </Menu>
                {extras.map((item) => (
                  <Button key={item} color="inherit">
                    {item}
                  </Button>
                ))}
              </Box>
            </>
          )}
        </Toolbar>
      </AppBar>
    </>
  );
}
