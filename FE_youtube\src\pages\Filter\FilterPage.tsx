import {
  Box,
  Paper,
  Typography,
  FormControl,
  Select,
  MenuItem,
  Container,
} from "@mui/material";
import { useState } from "react";
import type { SelectChangeEvent } from "@mui/material";
import TopSubscribedCreators from "../Youtube/components/TopSubscribedCreators";

// Filter options
const countries = [
  { value: "vn", label: "Vietnam" },
  { value: "all", label: "All Countries" },
  { value: "us", label: "United States" },
  { value: "uk", label: "United Kingdom" },
  { value: "jp", label: "Japan" },
];

const categories = [
  { value: "all", label: "All" },
  { value: "gaming", label: "Gaming" },
  { value: "music", label: "Music" },
  { value: "education", label: "Education" },
];

const lengthOptions = [
  { value: 50, label: "50" },
  { value: 100, label: "100" },
  { value: 200, label: "200" },
];

const typeOptions = [
  { value: "top", label: "Top" },
  { value: "bottom", label: "Bottom" },
];

const orderByOptions = [
  { value: "sbrank", label: "SB Rank" },
  { value: "views", label: "Views" },
  { value: "subscribers", label: "Subscribers" },
  { value: "videos", label: "Videos" },
];

const filterOptions = [
  { value: "all", label: "Show Everyone" },
  { value: "hide", label: "Hide Made for Kids" },
  { value: "only", label: "Show Made for Kids Only" },
];

const FilterTopList: React.FC = () => {
  const [filters, setFilters] = useState({
    country: "global",
    category: "all",
    length: 100,
    type: "top",
    orderBy: "sbrank",
    filter: "all",
  });

  const handleChange = (event: SelectChangeEvent<string | number>) => {
    const { name, value } = event.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 3,
        borderRadius: 2,
        backgroundColor: "#f8f9fa",
        border: "1px solid #e0e0e0",
      }}
    >
      {/* Header */}
      <Box sx={{ mb: 3, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
        <Typography variant="h2" component="h2" sx={{ fontWeight: "bold", fontSize: "25px" }}>
          Filter Top List
        </Typography>
      </Box>

      {/* Filter Controls - Split into left and right sections */}
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "flex-end", gap: 4, flexWrap: "wrap" }}>
        {/* Left side filters */}
        <Box sx={{ display: "flex", gap: 3, alignItems: "flex-end", flex: 1 }}>
          {/* Country Filter */}
          <Box sx={{ minWidth: 140 }}>
            <Typography variant="body2" sx={{ display: "block", mb: 1, fontWeight: "medium", fontSize: "14px" }}>
              Country
            </Typography>
            <FormControl size="medium" sx={{ minWidth: 140 }}>
              <Select
                name="country"
                value={filters.country}
                onChange={handleChange}
                displayEmpty
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d0d0d0",
                  },
                  fontSize: "14px",
                  height: "40px"
                }}
              >
                <MenuItem value="global">Global</MenuItem>
                {countries.map((country) => (
                  <MenuItem key={country.value} value={country.value}>
                    {country.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Category Filter */}
          <Box sx={{ minWidth: 140 }}>
            <Typography variant="body2" sx={{ display: "block", mb: 1, fontWeight: "medium", fontSize: "14px" }}>
              Category
            </Typography>
            <FormControl size="medium" sx={{ minWidth: 140 }}>
              <Select
                name="category"
                value={filters.category}
                onChange={handleChange}
                displayEmpty
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d0d0d0",
                  },
                  fontSize: "14px",
                  height: "40px"
                }}
              >
                {categories.map((category) => (
                  <MenuItem key={category.value} value={category.value}>
                    {category.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Length Filter */}
          <Box sx={{ minWidth: 100 }}>
            <Typography variant="body2" sx={{ display: "block", mb: 1, fontWeight: "medium", fontSize: "14px" }}>
              Length
            </Typography>
            <FormControl size="medium" sx={{ minWidth: 100 }}>
              <Select
                name="length"
                value={filters.length}
                onChange={handleChange}
                displayEmpty
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d0d0d0",
                  },
                  fontSize: "14px",
                  height: "40px"
                }}
              >
                {lengthOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Filter */}
          <Box sx={{ minWidth: 160 }}>
            <Typography variant="body2" sx={{ display: "block", mb: 1, fontWeight: "medium", fontSize: "14px" }}>
              Filter
            </Typography>
            <FormControl size="medium" sx={{ minWidth: 160 }}>
              <Select
                name="filter"
                value={filters.filter}
                onChange={handleChange}
                displayEmpty
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d0d0d0",
                  },
                  fontSize: "14px",
                  height: "40px"
                }}
              >
                <MenuItem value="all">Show Everyone</MenuItem>
                {filterOptions.slice(1).map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* Right side filters */}
        <Box sx={{ display: "flex", gap: 3, alignItems: "flex-end" }}>
          {/* Type Filter */}
          <Box sx={{ minWidth: 100 }}>
            <Typography variant="body2" sx={{ display: "block", mb: 1, fontWeight: "medium", fontSize: "14px" }}>
              Type
            </Typography>
            <FormControl size="medium" sx={{ minWidth: 100 }}>
              <Select
                name="type"
                value={filters.type}
                onChange={handleChange}
                displayEmpty
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d0d0d0",
                  },
                  fontSize: "14px",
                  height: "40px"
                }}
              >
                {typeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Order By Filter */}
          <Box sx={{ minWidth: 120 }}>
            <Typography variant="body2" sx={{ display: "block", mb: 1, fontWeight: "medium", fontSize: "14px" }}>
              Order By
            </Typography>
            <FormControl size="medium" sx={{ minWidth: 120 }}>
              <Select
                name="orderBy"
                value={filters.orderBy}
                onChange={handleChange}
                displayEmpty
                sx={{
                  backgroundColor: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#d0d0d0",
                  },
                  fontSize: "14px",
                  height: "40px"
                }}
              >
                {orderByOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

const FilterPage: React.FC = () => {
  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 8 }}>
      <Paper elevation={2} sx={{ p: 4 }}>
        <FilterTopList />

        {/* Top Subscribers YouTube Creators Table */}
        <Box sx={{ mt: 4 }}>
          <TopSubscribedCreators />
        </Box>
      </Paper>
    </Container>
  );
};

export default FilterPage;
