import {
    <PERSON>A<PERSON>s,
    <PERSON><PERSON><PERSON><PERSON>,
    CartesianGrid,
    Tooltip,
    AreaChart,
    Area,
    ResponsiveContainer,
} from 'recharts';
function SynchronizedLineChart({ data, type }: { data: { subscribers?: number, views?: number; date: string }[], type: string }) {
    return (
        <ResponsiveContainer width="100%" height={200}>
            <AreaChart
                width={500}
                height={200}
                data={data}
                syncId="anyId"
                margin={{
                    top: 10,
                    right: 30,
                    left: 70,
                    bottom: 0,
                }}
            >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey={type} stroke="#9D2F22" fill="#9D2F22" />
            </AreaChart>
        </ResponsiveContainer>
    );
}

export default SynchronizedLineChart;