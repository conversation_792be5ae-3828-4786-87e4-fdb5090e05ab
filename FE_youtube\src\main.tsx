
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import {
  RouterProvider,
} from "react-router";
import './index.css'
import App from './App.tsx'
import router from './routes/index.tsx';

import { Provider } from 'react-redux'
import { store } from './services/redux/store.tsx'



createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>


      <RouterProvider router={router} />
      <App />
    </Provider>
  </StrictMode>,
)

