import Grid from "@mui/material/Grid";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";

import Box from "@mui/material/Box";


const stats = [
  { label: "Tổng người dùng", value: "12,430" },
  { label: "Tổng lượt sub", value: "342,000+" },
  { label: "Tổng like đã trao đổi", value: "1,230,000+" },
];

export default function StatsOverview() {
  return (
    <Box display="flex" justifyContent="center">
      <Grid container spacing={3} maxWidth="lg" justifyContent="center">
        {stats.map((stat, i) => (
          <Grid item xs={12} sm={6} md={4} key={i}>
            <Paper
              elevation={3}
              sx={{
                py: 6,
                px: 3,
                textAlign: "center",
                borderRadius: 3,
              }}
            >
              <Typography variant="h6" color="text.secondary">
                {stat.label}
              </Typography>
              <Typography variant="h4" color="primary" fontWeight="bold">
                {stat.value}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
