import {
  Box,
  Typography,
  Avatar,
  Grid,
  Container,
  Paper,
} from "@mui/material";

// <PERSON>h sách các nhà sáng tạo YouTube ở Việt Nam
const youtubeCreators = [
  { name: "ToRung", avatar: "/avatars/torung.png" },
  { name: "BEN EAGLE", avatar: "/avatars/ben-eagle.png" },
  { name: "H&T Official", avatar: "/avatars/ht.png" },
  { name: "Like Nastya VNM", avatar: "/avatars/nastya.png" },
  { name: "POPS Kids", avatar: "/avatars/pops.png" },
  { name: "Trần Thị Thu Trang", avatar: "/avatars/trang.png" },
  { name: "<PERSON>", avatar: "/avatars/nam.png" },
];

// Danh sách các nhà sáng tạo TikTok phổ biến
const tiktokCreators = [
  { name: "<PERSON><PERSON><PERSON> lame", avatar: "/avatars/khabane.png" },
  { name: "charli d'amelio", avatar: "/avatars/charli.png" },
  { name: "Mr<PERSON>east", avatar: "/avatars/mrbeast.png" },
  { name: "Bella Poarch", avatar: "/avatars/bella.png" },
  { name: "TikTok", avatar: "/avatars/tiktok.png" },
  { name: "Addison", avatar: "/avatars/addison.png" },
  { name: "Kimberly Loaiza", avatar: "/avatars/kimberly.png" },
];

// Thống kê các nền tảng
const platformStats = [
  { label: "Total Creators", value: "90M" },
  { label: "YouTube", value: "69M" },
  { label: "Instagram", value: "11M" },
  { label: "TikTok", value: "1.5M" },
  { label: "Twitch", value: "7.3M" },
  { label: "Facebook", value: "1.8M" },
];

// Component thẻ nhà sáng tạo
type CreatorCardProps = {
  avatar: string;
  name: string;
};

function CreatorCard({ avatar, name }: CreatorCardProps) {
  return (
    <Paper
      elevation={1}
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 1.5,
        px: 2,
        py: 1,
        borderRadius: 1,
        backgroundColor: "rgba(255, 255, 255, 0.15)",
        minWidth: 160,
      }}
    >
      <Avatar
        src={avatar}
        alt={name}
        sx={{
          width: 30,
          height: 30,
          bgcolor: "rgba(255, 255, 255, 0.9)",
        }}
      />
      <Typography
        variant="body2"
        sx={{
          color: "#ffffff",
          fontWeight: 500,
        }}
      >
        {name}
      </Typography>
    </Paper>
  );
}

export default function SocialBladeAnalytics() {
  return (
    <Box
      sx={{
        backgroundColor: "#B73528",
        color: "white",
        position: "relative",
        overflow: "hidden",
        pt: 5,
        pb: 8,
        backgroundImage: `
          radial-gradient(circle at top right, rgba(255, 255, 255, 0.05) 0%, rgba(0, 0, 0, 0) 60%),
          radial-gradient(circle at bottom left, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 60%)
        `,
      }}
    >
      <Container maxWidth="lg">
        {/* Tiêu đề chính */}
        <Typography
          variant="h2"
          component="h1"
          align="center"
          fontWeight={700}
          letterSpacing={1}
          sx={{ mb: 1, fontSize: { xs: "2.5rem", sm: "3.5rem" } }}
        >
          ANALYTICS MADE EASY
        </Typography>
        
        {/* Mô tả */}
        <Typography
          variant="body1"
          align="center"
          sx={{ 
            mb: 6,
            mx: "auto",
            maxWidth: 800,
            fontSize: { xs: "0.95rem", sm: "1rem" },
            lineHeight: 1.5,
          }}
        >
          Social Blade tracks daily statistics and growth for your favorite content creators that spans
          across over 90 million users and 5 platforms.
        </Typography>

        {/* Nhà sáng tạo YouTube */}
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            mb: 2,
            fontSize: "1.1rem",
          }}
        >
          Popular YouTube Creators in Vietnam
        </Typography>
        
        <Box
          sx={{
            display: "flex",
            flexWrap: { xs: "nowrap", md: "wrap" },
            gap: 1.5,
            mb: 4,
            overflowX: "auto",
            pb: 1,
            "&::-webkit-scrollbar": {
              height: 4,
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "rgba(255, 255, 255, 0.2)",
              borderRadius: 2,
            },
          }}
        >
          {youtubeCreators.map((creator, i) => (
            <CreatorCard key={i} {...creator} />
          ))}
        </Box>

        {/* Nhà sáng tạo TikTok */}
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            mb: 2,
            fontSize: "1.1rem",
          }}
        >
          Popular TikTok Creators
        </Typography>
        
        <Box
          sx={{
            display: "flex",
            flexWrap: { xs: "nowrap", md: "wrap" },
            gap: 1.5,
            mb: 8,
            overflowX: "auto",
            pb: 1,
            "&::-webkit-scrollbar": {
              height: 4,
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "rgba(255, 255, 255, 0.2)",
              borderRadius: 2,
            },
          }}
        >
          {tiktokCreators.map((creator, i) => (
            <CreatorCard key={i} {...creator} />
          ))}
        </Box>

        {/* Thống kê nền tảng */}
        <Grid 
          container 
          spacing={2}
          justifyContent="center"
          sx={{ mt: 4 }}
        >
          {platformStats.map((stat, i) => (
            <Grid item xs={4} sm={4} md={2} key={i}>
              <Box sx={{ textAlign: "center" }}>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 700,
                    mb: 0.5,
                    fontSize: { xs: "1.8rem", sm: "2.2rem", md: "2.5rem" },
                  }}
                >
                  {stat.value}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    opacity: 0.85,
                    fontSize: "0.85rem",
                  }}
                >
                  {stat.label}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
}