import React from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Avatar,
} from "@mui/material";
import {
  Stars as StarsIcon,
  EmojiEvents as TrophyIcon,
  PlayArrow as PlayIcon,
  ThumbUp as LikeIcon,
  Comment as CommentIcon,
  Share as ShareIcon,
  PersonAdd as SubscribeIcon,
  CardGiftcard as GiftIcon,
  Notifications as NotificationIcon,
} from "@mui/icons-material";

// TypeScript Interfaces
interface PointActivity {
  id: string;
  date: string;
  action: string;
  points: number;
}

interface Level {
  name: string;
  minPoints: number;
  maxPoints: number;
  color: string;
  icon: string;
  badge: string;
}

interface Reward {
  id: string;
  name: string;
  description: string;
  requiredPoints: number;
  available: boolean;
}

interface EarnMethod {
  action: string;
  points: number;
  icon: React.ElementType;
  description: string;
}

const PointSystemPage: React.FC = () => {
  // Mock Data
  const currentPoints = 1250;
  const currentLevel = "Bronze Fan";
  const nextLevel = "Silver Fan";
  const pointsToNextLevel = 250;
  const progressPercentage = 75;

  const recentActivities: PointActivity[] = [
    { id: "1", date: "26/05/2025", action: "Watched 5 minutes video", points: 10 },
    { id: "2", date: "25/05/2025", action: "Commented on a video", points: 5 },
    { id: "3", date: "25/05/2025", action: "Shared a video", points: 15 },
    { id: "4", date: "24/05/2025", action: "Liked a video", points: 3 },
    { id: "5", date: "24/05/2025", action: "Subscribed to new channel", points: 20 },
    { id: "6", date: "23/05/2025", action: "Watched 10 minutes video", points: 20 },
    { id: "7", date: "23/05/2025", action: "Left a comment", points: 5 },
  ];

  const levels: Level[] = [
    { name: "Bronze Fan", minPoints: 0, maxPoints: 1499, color: "#CD7F32", icon: "🥉", badge: "bronze" },
    { name: "Silver Fan", minPoints: 1500, maxPoints: 2999, color: "#C0C0C0", icon: "🥈", badge: "silver" },
    { name: "Gold Fan", minPoints: 3000, maxPoints: 4999, color: "#FFD700", icon: "🥇", badge: "gold" },
    { name: "Platinum Fan", minPoints: 5000, maxPoints: 9999, color: "#E5E4E2", icon: "💎", badge: "platinum" },
  ];

  const rewards: Reward[] = [
    { id: "1", name: "Gift Card 20K VND", description: "Digital gift card", requiredPoints: 1000, available: true },
    { id: "2", name: "VIP Fan Badge", description: "Special profile badge", requiredPoints: 500, available: true },
    { id: "3", name: "iPhone Giveaway Entry", description: "Entry to monthly giveaway", requiredPoints: 2000, available: false },
    { id: "4", name: "Premium Features", description: "1 month premium access", requiredPoints: 1500, available: false },
  ];

  const earnMethods: EarnMethod[] = [
    { action: "Watch 1 minute of video", points: 2, icon: PlayIcon, description: "Earn points by watching content" },
    { action: "Like a video", points: 3, icon: LikeIcon, description: "Show appreciation for creators" },
    { action: "Leave a comment", points: 5, icon: CommentIcon, description: "Engage with the community" },
    { action: "Share to social media", points: 15, icon: ShareIcon, description: "Help spread great content" },
    { action: "Subscribe to new channel", points: 20, icon: SubscribeIcon, description: "Support your favorite creators" },
  ];

  const getCurrentLevelInfo = () => {
    return levels.find(level => currentPoints >= level.minPoints && currentPoints <= level.maxPoints) || levels[0];
  };



  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 8 }}>
      {/* Header Section */}
      <Paper elevation={2} sx={{ p: 4, mb: 4, backgroundColor: "#B73528", color: "white" }}>
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <StarsIcon sx={{ fontSize: 40 }} />
          <Typography variant="h3" sx={{ fontWeight: "bold" }}>
            Your Reward Points
          </Typography>
        </Box>
        <Typography variant="h6" sx={{ opacity: 0.9 }}>
          Earn points when you interact with content on YouTube. Collect points and redeem exciting rewards!
        </Typography>
      </Paper>

      {/* Current Points Dashboard */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Box display="flex" flexDirection={{ xs: "column", md: "row" }} gap={4} alignItems="center">
          <Box flex={1} textAlign={{ xs: "center", md: "left" }}>
            <Typography variant="h2" sx={{ fontWeight: "bold", color: "#B73528", mb: 1 }}>
              {currentPoints.toLocaleString()}
            </Typography>
            <Typography variant="h6" sx={{ color: "text.secondary", mb: 2 }}>
              Total Points
            </Typography>
            <Chip
              label={currentLevel}
              sx={{
                backgroundColor: getCurrentLevelInfo().color,
                color: "white",
                fontWeight: "bold",
                fontSize: "1rem"
              }}
            />
          </Box>

          <Box flex={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                Progress to {nextLevel}
              </Typography>
              <Typography variant="body2" sx={{ color: "text.secondary" }}>
                {pointsToNextLevel} points needed
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={progressPercentage}
              sx={{
                height: 12,
                borderRadius: 6,
                backgroundColor: "#f0f0f0",
                "& .MuiLinearProgress-bar": {
                  backgroundColor: "#B73528",
                  borderRadius: 6,
                },
              }}
            />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: "block" }}>
              {progressPercentage}% complete
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Notifications & Achievements */}
      <Box mb={4}>
        <Alert
          severity="info"
          icon={<NotificationIcon />}
          sx={{ mb: 2, backgroundColor: "#e3f2fd" }}
        >
          You need {pointsToNextLevel} more points to level up to {nextLevel}!
        </Alert>
        <Alert
          severity="success"
          icon={<TrophyIcon />}
          sx={{ backgroundColor: "#e8f5e8" }}
        >
          Congratulations! You've reached the 1000 points milestone! 🎉
        </Alert>
      </Box>

      {/* Main Content Layout */}
      <Box display="flex" flexDirection={{ xs: "column", lg: "row" }} gap={4}>
        {/* Left Column */}
        <Box flex={2}>
          {/* Point Activity Log */}
          <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
            <Typography variant="h5" sx={{ fontWeight: "bold", mb: 3, color: "#B73528" }}>
              Recent Activity
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                    <TableCell><strong>Date</strong></TableCell>
                    <TableCell><strong>Action</strong></TableCell>
                    <TableCell align="right"><strong>Points Earned</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentActivities.map((activity, index) => (
                    <TableRow
                      key={activity.id}
                      sx={{
                        backgroundColor: index % 2 === 0 ? "white" : "#fafafa",
                        "&:hover": { backgroundColor: "#f0f0f0" }
                      }}
                    >
                      <TableCell>{activity.date}</TableCell>
                      <TableCell>{activity.action}</TableCell>
                      <TableCell align="right">
                        <Chip
                          label={`+${activity.points}`}
                          size="small"
                          sx={{
                            backgroundColor: "#4caf50",
                            color: "white",
                            fontWeight: "bold"
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* How to Earn Points */}
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: "bold", mb: 3, color: "#B73528" }}>
              How to Earn Points
            </Typography>
            <Box display="flex" flexDirection="column" gap={2}>
              {earnMethods.map((method, index) => {
                const IconComponent = method.icon;
                return (
                  <Box
                    key={index}
                    display="flex"
                    alignItems="center"
                    gap={2}
                    p={2}
                    sx={{
                      backgroundColor: "#f9f9f9",
                      borderRadius: 2,
                      border: "1px solid #e0e0e0"
                    }}
                  >
                    <IconComponent sx={{ color: "#B73528", fontSize: 24 }} />
                    <Box flex={1}>
                      <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                        {method.action}
                      </Typography>
                      <Typography variant="body2" sx={{ color: "text.secondary" }}>
                        {method.description}
                      </Typography>
                    </Box>
                    <Chip
                      label={`+${method.points} pts`}
                      sx={{
                        backgroundColor: "#B73528",
                        color: "white",
                        fontWeight: "bold"
                      }}
                    />
                  </Box>
                );
              })}
            </Box>
          </Paper>
        </Box>

        {/* Right Column */}
        <Box flex={1}>
          {/* Levels & Badges Section */}
          <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
            <Typography variant="h5" sx={{ fontWeight: "bold", mb: 3, color: "#B73528" }}>
              Levels & Badges
            </Typography>
            <Box display="flex" flexDirection="column" gap={2}>
              {levels.map((level, index) => {
                const isCurrentLevel = level.name === currentLevel;
                const isCompleted = currentPoints > level.maxPoints;
                const isNext = level.name === nextLevel;

                return (
                  <Card
                    key={index}
                    sx={{
                      border: isCurrentLevel ? `2px solid ${level.color}` : "1px solid #e0e0e0",
                      backgroundColor: isCurrentLevel ? "#fff8f0" : isCompleted ? "#f0f8f0" : "white",
                      position: "relative"
                    }}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Box display="flex" alignItems="center" gap={2}>
                        <Avatar
                          sx={{
                            backgroundColor: level.color,
                            width: 40,
                            height: 40,
                            fontSize: "1.2rem"
                          }}
                        >
                          {level.icon}
                        </Avatar>
                        <Box flex={1}>
                          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                            {level.name}
                          </Typography>
                          <Typography variant="body2" sx={{ color: "text.secondary" }}>
                            {level.minPoints.toLocaleString()} - {level.maxPoints.toLocaleString()} points
                          </Typography>
                        </Box>
                        {isCurrentLevel && (
                          <Chip
                            label="Current"
                            size="small"
                            sx={{
                              backgroundColor: level.color,
                              color: "white",
                              fontWeight: "bold"
                            }}
                          />
                        )}
                        {isCompleted && !isCurrentLevel && (
                          <Chip
                            label="Completed"
                            size="small"
                            sx={{
                              backgroundColor: "#4caf50",
                              color: "white",
                              fontWeight: "bold"
                            }}
                          />
                        )}
                        {isNext && (
                          <Chip
                            label="Next"
                            size="small"
                            sx={{
                              backgroundColor: "#ff9800",
                              color: "white",
                              fontWeight: "bold"
                            }}
                          />
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                );
              })}
            </Box>
          </Paper>

          {/* Redeem Rewards Section */}
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: "bold", mb: 3, color: "#B73528" }}>
              Redeem Rewards
            </Typography>
            <Box display="flex" flexDirection="column" gap={2}>
              {rewards.map((reward) => {
                const canRedeem = currentPoints >= reward.requiredPoints;

                return (
                  <Card
                    key={reward.id}
                    sx={{
                      border: "1px solid #e0e0e0",
                      backgroundColor: canRedeem ? "#f0f8f0" : "#f9f9f9",
                      opacity: canRedeem ? 1 : 0.7
                    }}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <GiftIcon sx={{ color: "#B73528", fontSize: 24 }} />
                        <Box flex={1}>
                          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                            {reward.name}
                          </Typography>
                          <Typography variant="body2" sx={{ color: "text.secondary" }}>
                            {reward.description}
                          </Typography>
                        </Box>
                      </Box>

                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Chip
                          label={`${reward.requiredPoints.toLocaleString()} points`}
                          sx={{
                            backgroundColor: "#B73528",
                            color: "white",
                            fontWeight: "bold"
                          }}
                        />
                        <Button
                          variant={canRedeem ? "contained" : "outlined"}
                          disabled={!canRedeem}
                          sx={{
                            backgroundColor: canRedeem ? "#B73528" : "transparent",
                            borderColor: "#B73528",
                            color: canRedeem ? "white" : "#B73528",
                            "&:hover": {
                              backgroundColor: canRedeem ? "#9D2F22" : "rgba(183, 53, 40, 0.04)"
                            },
                            "&:disabled": {
                              borderColor: "#ccc",
                              color: "#ccc"
                            }
                          }}
                        >
                          {canRedeem ? "Redeem" : "Not Enough Points"}
                        </Button>
                      </Box>

                      {!canRedeem && (
                        <Typography variant="caption" sx={{ color: "error.main", mt: 1, display: "block" }}>
                          Need {(reward.requiredPoints - currentPoints).toLocaleString()} more points
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </Box>
          </Paper>
        </Box>
      </Box>
    </Container>
  );
};

export default PointSystemPage;